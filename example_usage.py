#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抖音视频发布API使用示例

这个示例展示了如何使用抖音视频发布接口
"""

import requests
import json

# API服务地址
API_BASE_URL = "http://localhost:8001/api/douyin"

def publish_video_example():
    """视频发布示例"""
    
    # 发布参数
    publish_data = {
        "cookies": "your_login_cookies_here",  # 通过登录接口获取
        "video": "/path/to/your/video.mp4",    # 本地视频文件路径
        "description": "这是我的视频标题和描述内容",  # 视频描述
        "proxy": None,  # 可选：代理地址
        "use_cdp": False,  # 可选：是否使用CDP模式
        "session_id": None  # 可选：会话ID
    }
    
    try:
        # 发送发布请求
        response = requests.post(
            f"{API_BASE_URL}/creator/publish",
            json=publish_data,
            timeout=300  # 5分钟超时
        )
        
        # 检查响应
        if response.status_code == 200:
            result = response.json()
            print("发布结果:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            if result.get("status") == "success":
                print("✅ 视频发布成功！")
            else:
                print("❌ 视频发布失败")
                print(f"错误信息: {result.get('message')}")
        else:
            print(f"请求失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except requests.exceptions.Timeout:
        print("请求超时，视频可能正在上传中...")
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")

def get_qrcode_login_example():
    """二维码登录示例"""
    
    try:
        # 1. 获取二维码
        qr_response = requests.post(f"{API_BASE_URL}/creator/qrcode/get", json={
            "use_cdp": False,
            "proxy": None
        })
        
        if qr_response.status_code != 200:
            print(f"获取二维码失败: {qr_response.text}")
            return None
        
        qr_data = qr_response.json()
        session_id = qr_data["session_id"]
        print(f"二维码获取成功，会话ID: {session_id}")
        print("请使用抖音APP扫描二维码登录...")
        
        # 2. 检查登录状态（这里只检查一次，实际使用中需要轮询）
        status_response = requests.post(f"{API_BASE_URL}/creator/qrcode/status", json={
            "session_id": session_id
        })
        
        if status_response.status_code == 200:
            status_data = status_response.json()
            print(f"登录状态: {status_data.get('login_status')}")
            
            if status_data.get("login_status") == "logged_in":
                cookies = status_data.get("cookies", "")
                print("登录成功！")
                return cookies
        
        return None
        
    except requests.exceptions.RequestException as e:
        print(f"登录请求异常: {e}")
        return None

if __name__ == "__main__":
    print("抖音视频发布API使用示例")
    print("=" * 50)
    
    # 示例1: 获取登录cookies（实际使用中需要完整的轮询逻辑）
    print("1. 二维码登录示例:")
    cookies = get_qrcode_login_example()
    
    print("\n" + "=" * 50)
    
    # 示例2: 发布视频（需要有效的cookies和视频文件）
    print("2. 视频发布示例:")
    print("注意：请先修改publish_data中的cookies和video路径")
    # publish_video_example()  # 取消注释以运行发布示例
    
    print("\n使用说明:")
    print("1. 首先通过二维码登录获取cookies")
    print("2. 准备好要发布的视频文件")
    print("3. 修改publish_data中的参数")
    print("4. 取消注释publish_video_example()调用")
    print("5. 运行脚本进行发布")
