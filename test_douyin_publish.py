#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抖音视频发布接口测试脚本

使用方法:
1. 先通过二维码登录获取cookies
2. 准备好要发布的视频文件
3. 运行此脚本测试发布功能

注意: 这是测试脚本，请在测试环境中使用
"""

import asyncio
import httpx
import json
import os
import time
from typing import Dict, Any

# API服务地址
API_BASE_URL = "http://localhost:8001/api/douyin"

async def test_qrcode_login() -> str:
    """测试二维码登录流程"""
    print("=== 测试二维码登录 ===")
    
    async with httpx.AsyncClient(timeout=60.0) as client:
        # 1. 获取二维码
        print("1. 获取登录二维码...")
        response = await client.post(f"{API_BASE_URL}/creator/qrcode/get", json={
            "use_cdp": False,
            "proxy": None
        })
        
        if response.status_code != 200:
            print(f"获取二维码失败: {response.text}")
            return ""
        
        qr_data = response.json()
        session_id = qr_data["session_id"]
        print(f"二维码获取成功，会话ID: {session_id}")
        print("请使用抖音APP扫描二维码登录...")
        
        # 2. 轮询检查登录状态
        print("2. 等待扫码登录...")
        max_attempts = 30  # 最多等待5分钟
        for attempt in range(max_attempts):
            await asyncio.sleep(10)  # 每10秒检查一次
            
            status_response = await client.post(f"{API_BASE_URL}/creator/qrcode/status", json={
                "session_id": session_id
            })
            
            if status_response.status_code != 200:
                print(f"检查登录状态失败: {status_response.text}")
                continue
            
            status_data = status_response.json()
            login_status = status_data.get("login_status")
            
            if login_status == "logged_in":
                cookies = status_data.get("cookies", "")
                print("登录成功！")
                print(f"Cookies: {cookies[:100]}...")  # 只显示前100个字符
                return cookies
            elif login_status == "waiting":
                print(f"等待扫码中... ({attempt + 1}/{max_attempts})")
            else:
                print(f"登录状态: {login_status}, 消息: {status_data.get('message', '')}")
        
        print("登录超时")
        return ""

async def test_video_publish(cookies: str, video_path: str, description: str, use_cdp: bool = False) -> Dict[str, Any]:
    """测试视频发布"""
    print("=== 测试视频发布 ===")

    # 检查视频文件是否存在
    if not os.path.exists(video_path):
        print(f"视频文件不存在: {video_path}")
        return {"status": "error", "message": "视频文件不存在"}

    # 获取视频文件信息
    file_size = os.path.getsize(video_path)
    file_size_mb = file_size / (1024 * 1024)

    print(f"视频文件: {video_path}")
    print(f"文件大小: {file_size_mb:.2f} MB")
    print(f"视频描述: {description}")
    print(f"使用CDP模式: {use_cdp}")

    async with httpx.AsyncClient(timeout=600.0) as client:  # 10分钟超时
        try:
            response = await client.post(f"{API_BASE_URL}/creator/publish", json={
                "cookies": cookies,
                "video": video_path,
                "description": description,
                "proxy": None,
                "use_cdp": use_cdp
            })

            if response.status_code != 200:
                print(f"发布请求失败 (HTTP {response.status_code}): {response.text}")
                return {"status": "error", "message": f"HTTP {response.status_code}: {response.text}"}

            result = response.json()
            print("发布结果:")
            print(json.dumps(result, indent=2, ensure_ascii=False))

            return result

        except httpx.TimeoutException:
            print("请求超时，这可能是因为视频文件较大或网络较慢")
            return {"status": "timeout", "message": "请求超时"}
        except Exception as e:
            print(f"发布请求异常: {e}")
            return {"status": "error", "message": str(e)}

async def main():
    """主测试流程"""
    print("抖音视频发布接口测试")
    print("=" * 50)

    # 配置测试参数
    TEST_VIDEO_PATH = input("请输入测试视频文件路径: ").strip()
    TEST_DESCRIPTION = input("请输入视频描述: ").strip() or "测试视频发布"

    if not TEST_VIDEO_PATH:
        print("必须提供视频文件路径")
        return

    # 选择测试模式
    print("\n选择测试模式:")
    print("1. 完整测试（二维码登录 + 视频发布）")
    print("2. 仅测试视频发布（需要提供cookies）")

    mode = input("请选择模式 (1/2): ").strip()

    cookies = ""

    if mode == "1":
        # 完整测试流程
        cookies = await test_qrcode_login()
        if not cookies:
            print("登录失败，无法继续测试")
            return
    elif mode == "2":
        # 仅测试发布
        cookies = input("请输入cookies: ").strip()
        if not cookies:
            print("必须提供cookies")
            return
    else:
        print("无效的选择")
        return

    # 选择浏览器模式
    print("\n选择浏览器模式:")
    print("1. 标准模式（默认）")
    print("2. CDP模式（更好的反检测）")

    browser_mode = input("请选择模式 (1/2): ").strip() or "1"
    use_cdp = browser_mode == "2"

    # 确认发布信息
    print("\n发布信息确认:")
    print(f"视频文件: {TEST_VIDEO_PATH}")
    print(f"视频描述: {TEST_DESCRIPTION}")
    print(f"浏览器模式: {'CDP模式' if use_cdp else '标准模式'}")

    confirm = input("\n确认发布? (y/n): ").strip().lower()
    if confirm != 'y':
        print("已取消发布")
        return

    # 测试视频发布
    print("\n" + "=" * 50)
    print("开始发布视频，请耐心等待...")

    # 显示进度提示
    start_time = time.time()

    # 创建异步任务
    publish_task = asyncio.create_task(
        test_video_publish(cookies, TEST_VIDEO_PATH, TEST_DESCRIPTION, use_cdp)
    )

    # 显示等待动画
    chars = "|/-\\"
    i = 0
    while not publish_task.done():
        elapsed = time.time() - start_time
        print(f"\r发布中... {chars[i]} (已用时: {elapsed:.1f}秒)", end="")
        i = (i + 1) % len(chars)
        await asyncio.sleep(0.2)

    # 获取结果
    result = await publish_task

    # 输出最终结果
    elapsed_time = time.time() - start_time
    print(f"\n\n发布过程耗时: {elapsed_time:.2f}秒")
    print("=" * 50)
    print("测试完成")

    if result.get("status") == "success":
        print("✅ 视频发布成功！")
    elif result.get("status") == "timeout":
        print("⏱️ 请求超时，但发布可能仍在进行")
        print("请登录抖音创作者平台检查发布状态")
    else:
        print("❌ 视频发布失败")
        print(f"错误信息: {result.get('message', '未知错误')}")

    print("\n提示: 如果发布失败，请检查以下可能的原因:")
    print("1. cookies是否有效（可能需要重新登录）")
    print("2. 视频文件格式是否支持")
    print("3. 网络连接是否稳定")
    print("4. 尝试使用CDP模式（可能有更好的反检测效果）")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
