#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抖音视频发布接口测试脚本

使用方法:
1. 先通过二维码登录获取cookies
2. 准备好要发布的视频文件
3. 运行此脚本测试发布功能

注意: 这是测试脚本，请在测试环境中使用
"""

import asyncio
import httpx
import json
import os
from typing import Dict, Any

# API服务地址
API_BASE_URL = "http://localhost:8001/api/douyin"

async def test_qrcode_login() -> str:
    """测试二维码登录流程"""
    print("=== 测试二维码登录 ===")
    
    async with httpx.AsyncClient(timeout=60.0) as client:
        # 1. 获取二维码
        print("1. 获取登录二维码...")
        response = await client.post(f"{API_BASE_URL}/creator/qrcode/get", json={
            "use_cdp": False,
            "proxy": None
        })
        
        if response.status_code != 200:
            print(f"获取二维码失败: {response.text}")
            return ""
        
        qr_data = response.json()
        session_id = qr_data["session_id"]
        print(f"二维码获取成功，会话ID: {session_id}")
        print("请使用抖音APP扫描二维码登录...")
        
        # 2. 轮询检查登录状态
        print("2. 等待扫码登录...")
        max_attempts = 30  # 最多等待5分钟
        for attempt in range(max_attempts):
            await asyncio.sleep(10)  # 每10秒检查一次
            
            status_response = await client.post(f"{API_BASE_URL}/creator/qrcode/status", json={
                "session_id": session_id
            })
            
            if status_response.status_code != 200:
                print(f"检查登录状态失败: {status_response.text}")
                continue
            
            status_data = status_response.json()
            login_status = status_data.get("login_status")
            
            if login_status == "logged_in":
                cookies = status_data.get("cookies", "")
                print("登录成功！")
                print(f"Cookies: {cookies[:100]}...")  # 只显示前100个字符
                return cookies
            elif login_status == "waiting":
                print(f"等待扫码中... ({attempt + 1}/{max_attempts})")
            else:
                print(f"登录状态: {login_status}, 消息: {status_data.get('message', '')}")
        
        print("登录超时")
        return ""

async def test_video_publish(cookies: str, video_path: str, description: str) -> Dict[str, Any]:
    """测试视频发布"""
    print("=== 测试视频发布 ===")
    
    # 检查视频文件是否存在
    if not os.path.exists(video_path):
        print(f"视频文件不存在: {video_path}")
        return {"status": "error", "message": "视频文件不存在"}
    
    print(f"视频文件: {video_path}")
    print(f"视频描述: {description}")
    
    async with httpx.AsyncClient(timeout=300.0) as client:  # 5分钟超时
        response = await client.post(f"{API_BASE_URL}/creator/publish", json={
            "cookies": cookies,
            "video": video_path,
            "description": description,
            "proxy": None,
            "use_cdp": False
        })
        
        if response.status_code != 200:
            print(f"发布请求失败: {response.text}")
            return {"status": "error", "message": response.text}
        
        result = response.json()
        print("发布结果:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        return result

async def main():
    """主测试流程"""
    print("抖音视频发布接口测试")
    print("=" * 50)
    
    # 配置测试参数
    TEST_VIDEO_PATH = input("请输入测试视频文件路径: ").strip()
    TEST_DESCRIPTION = input("请输入视频描述: ").strip() or "测试视频发布"
    
    if not TEST_VIDEO_PATH:
        print("必须提供视频文件路径")
        return
    
    # 选择测试模式
    print("\n选择测试模式:")
    print("1. 完整测试（二维码登录 + 视频发布）")
    print("2. 仅测试视频发布（需要提供cookies）")
    
    mode = input("请选择模式 (1/2): ").strip()
    
    cookies = ""
    
    if mode == "1":
        # 完整测试流程
        cookies = await test_qrcode_login()
        if not cookies:
            print("登录失败，无法继续测试")
            return
    elif mode == "2":
        # 仅测试发布
        cookies = input("请输入cookies: ").strip()
        if not cookies:
            print("必须提供cookies")
            return
    else:
        print("无效的选择")
        return
    
    # 测试视频发布
    print("\n" + "=" * 50)
    result = await test_video_publish(cookies, TEST_VIDEO_PATH, TEST_DESCRIPTION)
    
    # 输出最终结果
    print("\n" + "=" * 50)
    print("测试完成")
    if result.get("status") == "success":
        print("✅ 视频发布成功！")
    else:
        print("❌ 视频发布失败")
        print(f"错误信息: {result.get('message', '未知错误')}")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
