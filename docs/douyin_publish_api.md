# 抖音视频发布 API 接口文档

## 概述

本接口通过 CDP 和 Playwright 实现抖音视频发布功能，支持自动化上传视频文件并发布到抖音创作者平台。

## 功能特性

- ✅ 支持多种视频格式（mp4, mov, avi, mkv 等）
- ✅ 自动化视频上传和发布流程
- ✅ 支持自定义视频描述
- ✅ 支持代理设置
- ✅ 支持 CDP 和标准浏览器模式
- ✅ 完整的错误处理和状态检测
- ✅ 发布结果实时反馈
- ✅ 智能元素查找（多重备选策略）
- ✅ 详细的调试信息输出
- ✅ 页面加载状态检测
- ✅ 自动处理随机生成的 CSS 类名

## API 接口

### 1. 视频发布接口

**接口地址**: `POST /api/douyin/creator/publish`

**请求参数**:

```json
{
  "cookies": "登录后的cookies字符串",
  "video": "本地视频文件路径",
  "description": "视频描述（标题和简介）",
  "proxy": "代理地址（可选）",
  "use_cdp": false,
  "session_id": "会话ID（可选）"
}
```

**参数说明**:

| 参数        | 类型    | 必填 | 说明                           |
| ----------- | ------- | ---- | ------------------------------ |
| cookies     | string  | 是   | 通过登录接口获取的 cookies     |
| video       | string  | 是   | 本地视频文件的绝对路径         |
| description | string  | 是   | 视频标题和描述内容             |
| proxy       | string  | 否   | 代理地址，格式：http://ip:port |
| use_cdp     | boolean | 否   | 是否使用 CDP 模式，默认 false  |
| session_id  | string  | 否   | 会话标识符                     |

**响应示例**:

```json
{
  "status": "success",
  "message": "视频发布成功",
  "video_info": {
    "path": "/path/to/video.mp4",
    "size_mb": 15.6,
    "format": ".mp4"
  },
  "publish_result": {
    "status": "success",
    "message": "视频发布成功"
  }
}
```

## 使用流程

### 1. 获取登录 Cookies

首先需要通过二维码登录获取 cookies：

```bash
# 1. 获取二维码
curl -X POST "http://localhost:8001/api/douyin/creator/qrcode/get" \
  -H "Content-Type: application/json" \
  -d '{"use_cdp": false}'

# 2. 检查登录状态
curl -X POST "http://localhost:8001/api/douyin/creator/qrcode/status" \
  -H "Content-Type: application/json" \
  -d '{"session_id": "your_session_id"}'
```

### 2. 发布视频

使用获取的 cookies 发布视频：

```bash
curl -X POST "http://localhost:8001/api/douyin/creator/publish" \
  -H "Content-Type: application/json" \
  -d '{
    "cookies": "your_cookies_here",
    "video": "/path/to/your/video.mp4",
    "description": "这是我的视频标题和描述"
  }'
```

## 支持的视频格式

- `.mp4` - 推荐格式
- `.mov` - Apple 格式
- `.avi` - 经典格式
- `.mkv` - 高质量格式
- `.flv` - Flash 格式
- `.wmv` - Windows 格式
- `.m4v` - MPEG-4 格式
- `.mpg` / `.mpeg` - MPEG 格式
- `.3gp` - 移动设备格式

## 错误处理

### 常见错误码

| 错误码 | 说明       | 解决方案             |
| ------ | ---------- | -------------------- |
| 400    | 参数错误   | 检查必填参数是否完整 |
| 404    | 文件不存在 | 确认视频文件路径正确 |
| 500    | 服务器错误 | 检查服务器日志       |

### 错误响应示例

```json
{
  "status": "error",
  "message": "视频文件不存在: /path/to/video.mp4",
  "video_info": null,
  "publish_result": {
    "status": "error",
    "message": "验证视频文件时发生错误"
  }
}
```

## 注意事项

1. **文件路径**: 必须使用绝对路径，确保 API 服务能够访问到视频文件
2. **Cookies 有效期**: 登录 cookies 有时效性，过期后需要重新登录
3. **视频大小**: 虽然没有硬性限制，但建议视频文件不要过大以免上传超时
4. **发布频率**: 建议控制发布频率，避免触发平台限制
5. **网络环境**: 确保网络稳定，上传大文件时可能需要较长时间

## 故障排除

### 常见问题及解决方案

#### 1. "无法找到视频上传元素"错误

**原因**: 抖音页面的 CSS 类名包含随机字符串，或页面结构发生变化

**解决方案**:

- 检查 cookies 是否有效，尝试重新登录
- 使用 CDP 模式：`"use_cdp": true`
- 查看生成的调试截图和 HTML 文件
- 确保网络连接稳定，页面完全加载

#### 2. "无法找到或点击发布按钮"错误

**原因**: 页面元素未完全加载或发布按钮被禁用

**解决方案**:

- 确认视频已成功上传
- 检查视频描述是否填写成功
- 等待页面完全加载后再尝试
- 查看调试信息中的按钮列表

#### 3. 发布超时

**原因**: 视频文件过大或网络较慢

**解决方案**:

- 压缩视频文件大小
- 检查网络连接
- 增加请求超时时间
- 使用更稳定的网络环境

#### 4. Cookies 过期

**症状**: 页面跳转到登录页面

**解决方案**:

- 重新通过二维码登录获取新的 cookies
- 检查 cookies 格式是否正确
- 确保 cookies 包含必要的认证信息

### 调试信息

当发布失败时，系统会自动生成以下调试文件：

- `debug_screenshot_*.png`: 页面截图
- `upload_page_debug.html`: 页面 HTML 源码
- 控制台日志: 详细的元素查找过程

这些信息可以帮助诊断问题所在。

## 测试脚本

项目提供了完整的测试脚本 `test_douyin_publish.py`，可以用于测试接口功能：

```bash
python test_douyin_publish.py
```

## 技术实现

- **浏览器自动化**: 使用 Playwright 进行页面操作
- **反检测**: 集成 stealth.min.js 防止检测
- **代理支持**: 支持 HTTP/SOCKS5 代理
- **CDP 模式**: 支持 Chrome DevTools Protocol 模式
- **错误恢复**: 完善的异常处理和重试机制

## 开发者信息

- 基于 MediaCrawler 项目开发
- 遵循项目许可协议
- 仅供学习和研究使用
