# 抖音视频发布API接口文档

## 概述

本接口通过CDP和Playwright实现抖音视频发布功能，支持自动化上传视频文件并发布到抖音创作者平台。

## 功能特性

- ✅ 支持多种视频格式（mp4, mov, avi, mkv等）
- ✅ 自动化视频上传和发布流程
- ✅ 支持自定义视频描述
- ✅ 支持代理设置
- ✅ 支持CDP和标准浏览器模式
- ✅ 完整的错误处理和状态检测
- ✅ 发布结果实时反馈

## API接口

### 1. 视频发布接口

**接口地址**: `POST /api/douyin/creator/publish`

**请求参数**:

```json
{
  "cookies": "登录后的cookies字符串",
  "video": "本地视频文件路径",
  "description": "视频描述（标题和简介）",
  "proxy": "代理地址（可选）",
  "use_cdp": false,
  "session_id": "会话ID（可选）"
}
```

**参数说明**:

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| cookies | string | 是 | 通过登录接口获取的cookies |
| video | string | 是 | 本地视频文件的绝对路径 |
| description | string | 是 | 视频标题和描述内容 |
| proxy | string | 否 | 代理地址，格式：http://ip:port |
| use_cdp | boolean | 否 | 是否使用CDP模式，默认false |
| session_id | string | 否 | 会话标识符 |

**响应示例**:

```json
{
  "status": "success",
  "message": "视频发布成功",
  "video_info": {
    "path": "/path/to/video.mp4",
    "size_mb": 15.6,
    "format": ".mp4"
  },
  "publish_result": {
    "status": "success",
    "message": "视频发布成功"
  }
}
```

## 使用流程

### 1. 获取登录Cookies

首先需要通过二维码登录获取cookies：

```bash
# 1. 获取二维码
curl -X POST "http://localhost:8001/api/douyin/creator/qrcode/get" \
  -H "Content-Type: application/json" \
  -d '{"use_cdp": false}'

# 2. 检查登录状态
curl -X POST "http://localhost:8001/api/douyin/creator/qrcode/status" \
  -H "Content-Type: application/json" \
  -d '{"session_id": "your_session_id"}'
```

### 2. 发布视频

使用获取的cookies发布视频：

```bash
curl -X POST "http://localhost:8001/api/douyin/creator/publish" \
  -H "Content-Type: application/json" \
  -d '{
    "cookies": "your_cookies_here",
    "video": "/path/to/your/video.mp4",
    "description": "这是我的视频标题和描述"
  }'
```

## 支持的视频格式

- `.mp4` - 推荐格式
- `.mov` - Apple格式
- `.avi` - 经典格式
- `.mkv` - 高质量格式
- `.flv` - Flash格式
- `.wmv` - Windows格式
- `.m4v` - MPEG-4格式
- `.mpg` / `.mpeg` - MPEG格式
- `.3gp` - 移动设备格式

## 错误处理

### 常见错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 400 | 参数错误 | 检查必填参数是否完整 |
| 404 | 文件不存在 | 确认视频文件路径正确 |
| 500 | 服务器错误 | 检查服务器日志 |

### 错误响应示例

```json
{
  "status": "error",
  "message": "视频文件不存在: /path/to/video.mp4",
  "video_info": null,
  "publish_result": {
    "status": "error",
    "message": "验证视频文件时发生错误"
  }
}
```

## 注意事项

1. **文件路径**: 必须使用绝对路径，确保API服务能够访问到视频文件
2. **Cookies有效期**: 登录cookies有时效性，过期后需要重新登录
3. **视频大小**: 虽然没有硬性限制，但建议视频文件不要过大以免上传超时
4. **发布频率**: 建议控制发布频率，避免触发平台限制
5. **网络环境**: 确保网络稳定，上传大文件时可能需要较长时间

## 测试脚本

项目提供了完整的测试脚本 `test_douyin_publish.py`，可以用于测试接口功能：

```bash
python test_douyin_publish.py
```

## 技术实现

- **浏览器自动化**: 使用Playwright进行页面操作
- **反检测**: 集成stealth.min.js防止检测
- **代理支持**: 支持HTTP/SOCKS5代理
- **CDP模式**: 支持Chrome DevTools Protocol模式
- **错误恢复**: 完善的异常处理和重试机制

## 开发者信息

- 基于MediaCrawler项目开发
- 遵循项目许可协议
- 仅供学习和研究使用
