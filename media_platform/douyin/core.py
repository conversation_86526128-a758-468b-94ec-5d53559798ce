# 声明：本代码仅供学习和研究目的使用。使用者应遵守以下原则：
# 1. 不得用于任何商业用途。
# 2. 使用时应遵守目标平台的使用条款和robots.txt规则。
# 3. 不得进行大规模爬取或对平台造成运营干扰。
# 4. 应合理控制请求频率，避免给目标平台带来不必要的负担。
# 5. 不得用于任何非法或不当的用途。
#
# 详细许可条款请参阅项目根目录下的LICENSE文件。
# 使用本代码即表示您同意遵守上述原则和LICENSE中的所有条款。


import asyncio
import os
import random
import time
from asyncio import Task
from typing import Any, Dict, List, Optional, Tuple

from playwright.async_api import (
    BrowserContext,
    BrowserType,
    Page,
    Playwright,
    async_playwright,
)

import config
from base.base_crawler import AbstractCrawler
from proxy.proxy_ip_pool import IpInfoModel, create_ip_pool
from store import douyin as douyin_store
from tools import utils
from tools.cdp_browser import CDPBrowserManager
from var import crawler_type_var, source_keyword_var

from .client import DOUYINClient
from .exception import DataFetchError
from .field import PublishTimeType
from .login import DouYinLogin


class DouYinCrawler(AbstractCrawler):
    context_page: Page
    dy_client: DOUYINClient
    browser_context: BrowserContext
    cdp_manager: Optional[CDPBrowserManager]

    def __init__(self) -> None:
        self.index_url = "https://www.douyin.com"
        self.cdp_manager = None
        self.creator_login_url = "https://creator.douyin.com/creator-micro/home"

    async def start(self) -> None:
        playwright_proxy_format, httpx_proxy_format = None, None
        if config.ENABLE_IP_PROXY:
            ip_proxy_pool = await create_ip_pool(
                config.IP_PROXY_POOL_COUNT, enable_validate_ip=True
            )
            ip_proxy_info: IpInfoModel = await ip_proxy_pool.get_proxy()
            playwright_proxy_format, httpx_proxy_format = self.format_proxy_info(
                ip_proxy_info
            )

        async with async_playwright() as playwright:
            # 根据配置选择启动模式
            if config.ENABLE_CDP_MODE:
                utils.logger.info("[DouYinCrawler] 使用CDP模式启动浏览器")
                self.browser_context = await self.launch_browser_with_cdp(
                    playwright,
                    playwright_proxy_format,
                    None,
                    headless=config.CDP_HEADLESS,
                )
            else:
                utils.logger.info("[DouYinCrawler] 使用标准模式启动浏览器")
                # Launch a browser context.
                chromium = playwright.chromium
                self.browser_context = await self.launch_browser(
                    chromium,
                    playwright_proxy_format,
                    user_agent=None,
                    headless=config.HEADLESS,
                )
            # stealth.min.js is a js script to prevent the website from detecting the crawler.
            await self.browser_context.add_init_script(path="libs/stealth.min.js")
            self.context_page = await self.browser_context.new_page()
            await self.context_page.goto(self.index_url)

            self.dy_client = await self.create_douyin_client(httpx_proxy_format)
            if not await self.dy_client.pong(browser_context=self.browser_context):
                login_obj = DouYinLogin(
                    login_type=config.LOGIN_TYPE,
                    login_phone="",  # you phone number
                    browser_context=self.browser_context,
                    context_page=self.context_page,
                    cookie_str=config.COOKIES,
                )
                await login_obj.begin()
                await self.dy_client.update_cookies(
                    browser_context=self.browser_context
                )
            crawler_type_var.set(config.CRAWLER_TYPE)
            if config.CRAWLER_TYPE == "search":
                # Search for notes and retrieve their comment information.
                await self.search()
            elif config.CRAWLER_TYPE == "detail":
                # Get the information and comments of the specified post
                await self.get_specified_awemes()
            elif config.CRAWLER_TYPE == "creator":
                # Get the information and comments of the specified creator
                await self.get_creators_and_videos()

            utils.logger.info("[DouYinCrawler.start] Douyin Crawler finished ...")

    async def search(self) -> None:
        utils.logger.info("[DouYinCrawler.search] Begin search douyin keywords")
        dy_limit_count = 10  # douyin limit page fixed value
        if config.CRAWLER_MAX_NOTES_COUNT < dy_limit_count:
            config.CRAWLER_MAX_NOTES_COUNT = dy_limit_count
        start_page = config.START_PAGE  # start page number
        for keyword in config.KEYWORDS.split(","):
            source_keyword_var.set(keyword)
            utils.logger.info(f"[DouYinCrawler.search] Current keyword: {keyword}")
            aweme_list: List[str] = []
            page = 0
            dy_search_id = ""
            while (
                page - start_page + 1
            ) * dy_limit_count <= config.CRAWLER_MAX_NOTES_COUNT:
                if page < start_page:
                    utils.logger.info(f"[DouYinCrawler.search] Skip {page}")
                    page += 1
                    continue
                try:
                    utils.logger.info(
                        f"[DouYinCrawler.search] search douyin keyword: {keyword}, page: {page}"
                    )
                    posts_res = await self.dy_client.search_info_by_keyword(
                        keyword=keyword,
                        offset=page * dy_limit_count - dy_limit_count,
                        publish_time=PublishTimeType(config.PUBLISH_TIME_TYPE),
                        search_id=dy_search_id,
                    )
                    if posts_res.get("data") is None or posts_res.get("data") == []:
                        utils.logger.info(
                            f"[DouYinCrawler.search] search douyin keyword: {keyword}, page: {page} is empty,{posts_res.get('data')}`"
                        )
                        break
                except DataFetchError:
                    utils.logger.error(
                        f"[DouYinCrawler.search] search douyin keyword: {keyword} failed"
                    )
                    break

                page += 1
                if "data" not in posts_res:
                    utils.logger.error(
                        f"[DouYinCrawler.search] search douyin keyword: {keyword} failed，账号也许被风控了。"
                    )
                    break
                dy_search_id = posts_res.get("extra", {}).get("logid", "")
                for post_item in posts_res.get("data"):
                    try:
                        aweme_info: Dict = (
                            post_item.get("aweme_info")
                            or post_item.get("aweme_mix_info", {}).get("mix_items")[0]
                        )
                    except TypeError:
                        continue
                    aweme_list.append(aweme_info.get("aweme_id", ""))
                    await douyin_store.update_douyin_aweme(aweme_item=aweme_info)
            utils.logger.info(
                f"[DouYinCrawler.search] keyword:{keyword}, aweme_list:{aweme_list}"
            )
            await self.batch_get_note_comments(aweme_list)

    async def get_specified_awemes(self):
        """Get the information and comments of the specified post"""
        semaphore = asyncio.Semaphore(config.MAX_CONCURRENCY_NUM)
        task_list = [
            self.get_aweme_detail(aweme_id=aweme_id, semaphore=semaphore)
            for aweme_id in config.DY_SPECIFIED_ID_LIST
        ]
        aweme_details = await asyncio.gather(*task_list)
        for aweme_detail in aweme_details:
            if aweme_detail is not None:
                await douyin_store.update_douyin_aweme(aweme_detail)
        await self.batch_get_note_comments(config.DY_SPECIFIED_ID_LIST)

    async def get_aweme_detail(
        self, aweme_id: str, semaphore: asyncio.Semaphore
    ) -> Any:
        """Get note detail"""
        async with semaphore:
            try:
                return await self.dy_client.get_video_by_id(aweme_id)
            except DataFetchError as ex:
                utils.logger.error(
                    f"[DouYinCrawler.get_aweme_detail] Get aweme detail error: {ex}"
                )
                return None
            except KeyError as ex:
                utils.logger.error(
                    f"[DouYinCrawler.get_aweme_detail] have not fund note detail aweme_id:{aweme_id}, err: {ex}"
                )
                return None

    async def batch_get_note_comments(self, aweme_list: List[str]) -> None:
        """
        Batch get note comments
        """
        if not config.ENABLE_GET_COMMENTS:
            utils.logger.info(
                f"[DouYinCrawler.batch_get_note_comments] Crawling comment mode is not enabled"
            )
            return

        task_list: List[Task] = []
        semaphore = asyncio.Semaphore(config.MAX_CONCURRENCY_NUM)
        for aweme_id in aweme_list:
            task = asyncio.create_task(
                self.get_comments(aweme_id, semaphore), name=aweme_id
            )
            task_list.append(task)
        if len(task_list) > 0:
            await asyncio.wait(task_list)

    async def get_comments(self, aweme_id: str, semaphore: asyncio.Semaphore) -> None:
        async with semaphore:
            try:
                # 将关键词列表传递给 get_aweme_all_comments 方法
                await self.dy_client.get_aweme_all_comments(
                    aweme_id=aweme_id,
                    crawl_interval=random.random(),
                    is_fetch_sub_comments=config.ENABLE_GET_SUB_COMMENTS,
                    callback=douyin_store.batch_update_dy_aweme_comments,
                    max_count=config.CRAWLER_MAX_COMMENTS_COUNT_SINGLENOTES,
                )
                utils.logger.info(
                    f"[DouYinCrawler.get_comments] aweme_id: {aweme_id} comments have all been obtained and filtered ..."
                )
            except DataFetchError as e:
                utils.logger.error(
                    f"[DouYinCrawler.get_comments] aweme_id: {aweme_id} get comments failed, error: {e}"
                )

    async def get_creators_and_videos(self) -> None:
        """
        Get the information and videos of the specified creator
        """
        utils.logger.info(
            "[DouYinCrawler.get_creators_and_videos] Begin get douyin creators"
        )
        for user_id in config.DY_CREATOR_ID_LIST:
            creator_info: Dict = await self.dy_client.get_user_info(user_id)
            if creator_info:
                await douyin_store.save_creator(user_id, creator=creator_info)

            # Get all video information of the creator
            all_video_list = await self.dy_client.get_all_user_aweme_posts(
                sec_user_id=user_id, callback=self.fetch_creator_video_detail
            )

            video_ids = [video_item.get("aweme_id") for video_item in all_video_list]
            await self.batch_get_note_comments(video_ids)

    async def fetch_creator_video_detail(self, video_list: List[Dict]):
        """
        Concurrently obtain the specified post list and save the data
        """
        semaphore = asyncio.Semaphore(config.MAX_CONCURRENCY_NUM)
        task_list = [
            self.get_aweme_detail(post_item.get("aweme_id"), semaphore)
            for post_item in video_list
        ]

        note_details = await asyncio.gather(*task_list)
        for aweme_item in note_details:
            if aweme_item is not None:
                await douyin_store.update_douyin_aweme(aweme_item)

    @staticmethod
    def format_proxy_info(
        ip_proxy_info: IpInfoModel,
    ) -> Tuple[Optional[Dict], Optional[Dict]]:
        """format proxy info for playwright and httpx"""
        playwright_proxy = {
            "server": f"{ip_proxy_info.protocol}{ip_proxy_info.ip}:{ip_proxy_info.port}",
            "username": ip_proxy_info.user,
            "password": ip_proxy_info.password,
        }
        httpx_proxy = {
            f"{ip_proxy_info.protocol}": f"http://{ip_proxy_info.user}:{ip_proxy_info.password}@{ip_proxy_info.ip}:{ip_proxy_info.port}"
        }
        return playwright_proxy, httpx_proxy

    async def create_douyin_client(self, httpx_proxy: Optional[str]) -> DOUYINClient:
        """Create douyin client"""
        cookie_str, cookie_dict = utils.convert_cookies(await self.browser_context.cookies())  # type: ignore
        douyin_client = DOUYINClient(
            proxies=httpx_proxy,
            headers={
                "User-Agent": await self.context_page.evaluate(
                    "() => navigator.userAgent"
                ),
                "Cookie": cookie_str,
                "Host": "www.douyin.com",
                "Origin": "https://www.douyin.com/",
                "Referer": "https://www.douyin.com/",
                "Content-Type": "application/json;charset=UTF-8",
            },
            playwright_page=self.context_page,
            cookie_dict=cookie_dict,
        )
        return douyin_client

    async def launch_browser(
        self,
        chromium: BrowserType,
        playwright_proxy: Optional[Dict],
        user_agent: Optional[str],
        headless: bool = True,
    ) -> BrowserContext:
        """Launch browser and create browser context"""
        if config.SAVE_LOGIN_STATE:
            user_data_dir = os.path.join(
                os.getcwd(), "browser_data", config.USER_DATA_DIR % config.PLATFORM
            )  # type: ignore
            browser_context = await chromium.launch_persistent_context(
                user_data_dir=user_data_dir,
                accept_downloads=True,
                headless=headless,
                proxy=playwright_proxy,  # type: ignore
                viewport={"width": 1920, "height": 1080},
                user_agent=user_agent,
            )  # type: ignore
            return browser_context
        else:
            browser = await chromium.launch(headless=headless, proxy=playwright_proxy)  # type: ignore
            browser_context = await browser.new_context(
                viewport={"width": 1920, "height": 1080}, user_agent=user_agent
            )
            return browser_context

    async def launch_browser_with_cdp(
        self,
        playwright: Playwright,
        playwright_proxy: Optional[Dict],
        user_agent: Optional[str],
        headless: bool = True,
    ) -> BrowserContext:
        """
        使用CDP模式启动浏览器
        """
        try:
            self.cdp_manager = CDPBrowserManager()
            browser_context = await self.cdp_manager.launch_and_connect(
                playwright=playwright,
                playwright_proxy=playwright_proxy,
                user_agent=user_agent,
                headless=headless,
            )

            # 添加反检测脚本
            await self.cdp_manager.add_stealth_script()

            # 显示浏览器信息
            browser_info = await self.cdp_manager.get_browser_info()
            utils.logger.info(f"[DouYinCrawler] CDP浏览器信息: {browser_info}")

            return browser_context

        except Exception as e:
            utils.logger.error(f"[DouYinCrawler] CDP模式启动失败，回退到标准模式: {e}")
            # 回退到标准模式
            chromium = playwright.chromium
            return await self.launch_browser(
                chromium, playwright_proxy, user_agent, headless
            )

    async def close(self) -> None:
        """Close browser context"""
        # 如果使用CDP模式，需要特殊处理
        if self.cdp_manager:
            await self.cdp_manager.cleanup()
            self.cdp_manager = None
        else:
            await self.browser_context.close()
        utils.logger.info("[DouYinCrawler.close] Browser context closed ...")
        
    async def creator_login(self, login_type: str, login_phone: str = "", cookie_str: str = "") -> Dict:
        """创作者平台登录接口
        
        Args:
            login_type: 登录类型，支持 'qrcode' 或 'cookie'
            login_phone: 手机号，当前创作者平台登录不支持手机号登录
            cookie_str: cookie字符串，当login_type为'cookie'时必须提供
            
        Returns:
            Dict: 包含登录状态和cookie信息的字典
        """
        utils.logger.info("[DouYinCrawler.creator_login] 开始创作者平台登录...")
        
        playwright_proxy_format = None
        if config.ENABLE_IP_PROXY:
            ip_proxy_pool = await create_ip_pool(
                config.IP_PROXY_POOL_COUNT, enable_validate_ip=True
            )
            ip_proxy_info: IpInfoModel = await ip_proxy_pool.get_proxy()
            playwright_proxy_format, _ = self.format_proxy_info(
                ip_proxy_info
            )

        async with async_playwright() as playwright:
            # 根据配置选择启动模式
            if config.ENABLE_CDP_MODE:
                utils.logger.info("[DouYinCrawler.creator_login] 使用CDP模式启动浏览器")
                self.browser_context = await self.launch_browser_with_cdp(
                    playwright,
                    playwright_proxy_format,
                    None,
                    headless=config.CDP_HEADLESS,
                )
            else:
                utils.logger.info("[DouYinCrawler.creator_login] 使用标准模式启动浏览器")
                # Launch a browser context.
                chromium = playwright.chromium
                self.browser_context = await self.launch_browser(
                    chromium,
                    playwright_proxy_format,
                    user_agent=None,
                    headless=config.HEADLESS,
                )
            # stealth.min.js is a js script to prevent the website from detecting the crawler.
            await self.browser_context.add_init_script(path="libs/stealth.min.js")
            self.context_page = await self.browser_context.new_page()
            await self.context_page.goto(self.creator_login_url)
            
            # 使用创作者登录类进行登录
            from .login import DouYinCreatorLogin
            login_obj = DouYinCreatorLogin(
                login_type=login_type,
                login_phone=login_phone,
                browser_context=self.browser_context,
                context_page=self.context_page,
                cookie_str=cookie_str,
            )
            await login_obj.begin()
            
            # 获取登录后的cookie信息
            cookie_str, cookie_dict = utils.convert_cookies(await self.browser_context.cookies())
            
            # 关闭浏览器
            await self.close()
            
            return {
                "status": "success",
                "message": "创作者平台登录成功",
                "cookies": cookie_str,
                "cookie_dict": cookie_dict
            }
            
    async def get_creator_data(self, cookie_str: str) -> Dict:
        """获取创作者数据
        
        Args:
            cookie_str: cookie字符串，通过creator_login方法获取
            
        Returns:
            Dict: 包含创作者数据的字典
        """
        utils.logger.info("[DouYinCrawler.get_creator_data] 开始获取创作者数据...")
        
        playwright_proxy_format, httpx_proxy_format = None, None
        if config.ENABLE_IP_PROXY:
            ip_proxy_pool = await create_ip_pool(
                config.IP_PROXY_POOL_COUNT, enable_validate_ip=True
            )
            ip_proxy_info: IpInfoModel = await ip_proxy_pool.get_proxy()
            playwright_proxy_format, httpx_proxy_format = self.format_proxy_info(
                ip_proxy_info
            )
            
        async with async_playwright() as playwright:
            # 根据配置选择启动模式
            if config.ENABLE_CDP_MODE:
                utils.logger.info("[DouYinCrawler.get_creator_data] 使用CDP模式启动浏览器")
                self.browser_context = await self.launch_browser_with_cdp(
                    playwright,
                    playwright_proxy_format,
                    None,
                    headless=config.CDP_HEADLESS,
                )
            else:
                utils.logger.info("[DouYinCrawler.get_creator_data] 使用标准模式启动浏览器")
                # Launch a browser context.
                chromium = playwright.chromium
                self.browser_context = await self.launch_browser(
                    chromium,
                    playwright_proxy_format,
                    user_agent=None,
                    headless=config.HEADLESS,
                )
                
            # 添加cookie
            for key, value in utils.convert_str_cookie_to_dict(cookie_str).items():
                await self.browser_context.add_cookies([{
                    'name': key,
                    'value': value,
                    'domain': ".douyin.com",
                    'path': "/"
                }])
                
            # stealth.min.js is a js script to prevent the website from detecting the crawler.
            await self.browser_context.add_init_script(path="libs/stealth.min.js")
            self.context_page = await self.browser_context.new_page()
            await self.context_page.goto(self.creator_login_url)
            
            # 创建抖音客户端
            self.dy_client = await self.create_douyin_client(httpx_proxy_format)
            
            # 获取创作者数据
            try:
                # 获取数据概览
                overview_data = await self.dy_client.get_creator_data_overview()
                
                # 获取内容列表
                content_list = await self.dy_client.get_creator_content_list()
                
                # 获取粉丝数据
                fans_data = await self.dy_client.get_creator_fans_data()
                
                # 关闭浏览器
                await self.close()
                
                return {
                    "status": "success",
                    "message": "获取创作者数据成功",
                    "data": {
                        "overview": overview_data,
                        "content_list": content_list,
                        "fans_data": fans_data
                    }
                }
            except Exception as e:
                # 关闭浏览器
                await self.close()
                
                return {
                    "status": "error",
                    "message": f"获取创作者数据失败: {str(e)}"
                }

    async def publish_video(self, cookie_str: str, video_path: str, description: str, proxy: Optional[str] = None) -> Dict:
        """发布视频到抖音创作者平台

        Args:
            cookie_str: 登录后的cookie字符串
            video_path: 本地视频文件路径
            description: 视频描述（标题和简介）
            proxy: 代理地址，格式为 "http://ip:port" 或 "socks5://ip:port"

        Returns:
            Dict: 包含发布状态和结果信息的字典
        """
        utils.logger.info("[DouYinCrawler.publish_video] 开始发布视频...")

        # 设置代理
        playwright_proxy_format = None
        if proxy:
            # 解析代理格式
            if proxy.startswith("http://") or proxy.startswith("https://"):
                playwright_proxy_format = {"server": proxy}
            elif proxy.startswith("socks5://"):
                playwright_proxy_format = {"server": proxy}
            else:
                # 默认添加http://前缀
                playwright_proxy_format = {"server": f"http://{proxy}"}
        elif config.ENABLE_IP_PROXY:
            ip_proxy_pool = await create_ip_pool(
                config.IP_PROXY_POOL_COUNT, enable_validate_ip=True
            )
            ip_proxy_info: IpInfoModel = await ip_proxy_pool.get_proxy()
            playwright_proxy_format, _ = self.format_proxy_info(
                ip_proxy_info
            )

        async with async_playwright() as playwright:
            try:
                # 根据配置选择启动模式
                if config.ENABLE_CDP_MODE:
                    utils.logger.info("[DouYinCrawler.publish_video] 使用CDP模式启动浏览器")
                    self.browser_context = await self.launch_browser_with_cdp(
                        playwright,
                        playwright_proxy_format,
                        None,
                        headless=config.CDP_HEADLESS,
                    )
                else:
                    utils.logger.info("[DouYinCrawler.publish_video] 使用标准模式启动浏览器")
                    # Launch a browser context.
                    chromium = playwright.chromium
                    self.browser_context = await self.launch_browser(
                        chromium,
                        playwright_proxy_format,
                        user_agent=None,
                        headless=config.HEADLESS,
                    )

                # 添加cookie
                for key, value in utils.convert_str_cookie_to_dict(cookie_str).items():
                    await self.browser_context.add_cookies([{
                        'name': key,
                        'value': value,
                        'domain': ".douyin.com",
                        'path': "/"
                    }])

                # stealth.min.js is a js script to prevent the website from detecting the crawler.
                await self.browser_context.add_init_script(path="libs/stealth.min.js")
                self.context_page = await self.browser_context.new_page()

                # 访问发布页面
                publish_url = "https://creator.douyin.com/creator-micro/content/post/video?enter_from=publish_page"
                utils.logger.info(f"[DouYinCrawler.publish_video] 访问发布页面: {publish_url}")
                await self.context_page.goto(publish_url, wait_until='domcontentloaded')

                # 等待页面完全加载
                utils.logger.info("[DouYinCrawler.publish_video] 等待页面完全加载...")
                await self.context_page.wait_for_load_state('domcontentloaded', timeout=15000)

                # 等待网络请求完成
                try:
                    await self.context_page.wait_for_load_state('networkidle', timeout=10000)
                except:
                    utils.logger.info("[DouYinCrawler.publish_video] 网络空闲等待超时，继续执行...")

                # 额外等待确保页面元素渲染完成
                await asyncio.sleep(3)

                # 检查页面是否正确加载
                page_title = await self.context_page.title()
                utils.logger.info(f"[DouYinCrawler.publish_video] 页面标题: {page_title}")

                # 如果页面标题不包含预期内容，可能需要重新登录
                if "创作者" not in page_title and "发布" not in page_title:
                    utils.logger.warning(f"[DouYinCrawler.publish_video] 页面标题异常，可能需要重新登录: {page_title}")
                    # 保存页面截图用于调试
                    await self.context_page.screenshot(path="page_load_debug.png")

                    # 检查是否跳转到登录页面
                    current_url = self.context_page.url
                    if "login" in current_url.lower():
                        raise Exception("页面跳转到登录页面，cookies可能已过期，请重新登录")

                # 执行视频发布流程
                result = await self._perform_video_publish(video_path, description)

                # 关闭浏览器
                await self.close()

                return result

            except Exception as e:
                # 确保浏览器被关闭
                try:
                    await self.close()
                except:
                    pass

                utils.logger.error(f"[DouYinCrawler.publish_video] 发布视频失败: {str(e)}")
                return {
                    "status": "error",
                    "message": f"发布视频失败: {str(e)}"
                }

    async def _perform_video_publish(self, video_path: str, description: str) -> Dict:
        """执行视频发布的具体操作

        Args:
            video_path: 视频文件路径
            description: 视频描述

        Returns:
            Dict: 发布结果
        """
        try:
            utils.logger.info("[DouYinCrawler._perform_video_publish] 开始执行发布操作...")

            # 1. 等待上传区域出现
            utils.logger.info("[DouYinCrawler._perform_video_publish] 等待视频上传区域...")

            # 使用更通用的选择器方式，避免依赖随机生成的类名
            upload_element = None

            # 尝试多种选择器策略
            try:
                # 策略1: 通过属性选择器查找
                upload_element = await self.context_page.wait_for_selector("input[type='file'][accept*='video']", timeout=5000)
                upload_selector = "input[type='file'][accept*='video']"
                utils.logger.info("[DouYinCrawler._perform_video_publish] 找到上传元素: input[type='file'][accept*='video']")
            except Exception as e:
                utils.logger.warning(f"[DouYinCrawler._perform_video_publish] 策略1失败: {e}")

                try:
                    # 策略2: 查找任何文件上传输入框
                    upload_element = await self.context_page.wait_for_selector("input[type='file']", timeout=5000)
                    upload_selector = "input[type='file']"
                    utils.logger.info("[DouYinCrawler._perform_video_publish] 找到上传元素: input[type='file']")
                except Exception as e:
                    utils.logger.warning(f"[DouYinCrawler._perform_video_publish] 策略2失败: {e}")

                    try:
                        # 策略3: 使用JavaScript查找所有文件输入元素
                        upload_elements = await self.context_page.evaluate("""
                            () => {
                                // 查找所有文件输入元素
                                const fileInputs = Array.from(document.querySelectorAll('input[type="file"]'));

                                // 返回元素的选择器路径
                                return fileInputs.map(el => {
                                    // 尝试构建一个唯一的选择器
                                    if (el.id) return `#${el.id}`;
                                    if (el.className) {
                                        const classes = el.className.split(' ').filter(c => c);
                                        if (classes.length > 0) return `input.${classes.join('.')}`;
                                    }

                                    // 如果没有ID或类，尝试使用属性
                                    const attrs = [];
                                    if (el.accept) attrs.push(`[accept="${el.accept}"]`);
                                    if (el.name) attrs.push(`[name="${el.name}"]`);

                                    return `input[type="file"]${attrs.join('')}`;
                                });
                            }
                        """)

                        if upload_elements and len(upload_elements) > 0:
                            # 尝试使用找到的选择器
                            for selector in upload_elements:
                                try:
                                    upload_element = await self.context_page.wait_for_selector(selector, timeout=2000)
                                    upload_selector = selector
                                    utils.logger.info(f"[DouYinCrawler._perform_video_publish] 找到上传元素: {selector}")
                                    break
                                except:
                                    continue
                    except Exception as e:
                        utils.logger.warning(f"[DouYinCrawler._perform_video_publish] 策略3失败: {e}")

            # 如果所有策略都失败，尝试最后的方法：通过类名部分匹配
            if not upload_element:
                try:
                    # 策略4: 查找包含特定关键字的类名
                    upload_element = await self.context_page.evaluate("""
                        () => {
                            // 查找可能的上传按钮类名关键字
                            const keywords = ['upload', 'btn', 'input', 'file'];
                            const inputs = Array.from(document.querySelectorAll('input[type="file"]'));

                            // 找到第一个匹配的元素
                            for (const input of inputs) {
                                if (input.className && keywords.some(keyword =>
                                    input.className.toLowerCase().includes(keyword.toLowerCase()))) {
                                    // 触发点击以确保元素可见
                                    input.click();
                                    return true;
                                }
                            }
                            return false;
                        }
                    """)

                    if upload_element:
                        # 如果JavaScript找到并点击了元素，使用通用选择器
                        upload_selector = "input[type='file']"
                        utils.logger.info("[DouYinCrawler._perform_video_publish] 通过JavaScript找到并点击了上传元素")
                except Exception as e:
                    utils.logger.warning(f"[DouYinCrawler._perform_video_publish] 策略4失败: {e}")

            # 如果仍然找不到上传元素，进行详细调试并抛出异常
            if not upload_element:
                utils.logger.error("[DouYinCrawler._perform_video_publish] 无法找到视频上传元素，开始调试...")

                # 调用调试函数
                await self._debug_page_elements("upload_element_not_found")

                # 获取页面HTML用于调试
                page_html = await self.context_page.content()
                with open("upload_page_debug.html", "w", encoding="utf-8") as f:
                    f.write(page_html)

                raise Exception("无法找到视频上传元素，已保存调试信息")

            # 2. 上传视频文件
            utils.logger.info(f"[DouYinCrawler._perform_video_publish] 开始上传视频: {video_path}")

            try:
                # 尝试使用选择器上传文件
                if upload_selector:
                    await self.context_page.set_input_files(upload_selector, video_path)
                else:
                    # 如果没有选择器，尝试直接使用JavaScript上传
                    await self.context_page.evaluate(f"""
                        (videoPath) => {{
                            const fileInputs = document.querySelectorAll('input[type="file"]');
                            if (fileInputs.length > 0) {{
                                // 创建文件对象并设置到第一个文件输入框
                                const input = fileInputs[0];
                                input.click();
                                return true;
                            }}
                            return false;
                        }}
                    """, video_path)

                    # 等待文件选择对话框并设置文件
                    await self.context_page.set_input_files("input[type='file']", video_path)

                utils.logger.info("[DouYinCrawler._perform_video_publish] 视频文件已设置到上传元素")

            except Exception as e:
                utils.logger.error(f"[DouYinCrawler._perform_video_publish] 上传文件失败: {e}")
                raise Exception(f"上传视频文件失败: {e}")

            # 3. 等待视频上传完成（检查上传进度或成功标识）
            utils.logger.info("[DouYinCrawler._perform_video_publish] 等待视频上传完成...")
            await asyncio.sleep(3)  # 给上传一些时间开始

            # 等待上传完成的标识
            upload_success_indicators = [
                ".upload-success",  # 上传成功标识
                ".video-preview",   # 视频预览
                "[data-testid='video-preview']",
                ".preview-container"
            ]

            upload_completed = False
            for indicator in upload_success_indicators:
                try:
                    await self.context_page.wait_for_selector(indicator, timeout=60000)  # 等待最多60秒
                    upload_completed = True
                    utils.logger.info(f"[DouYinCrawler._perform_video_publish] 检测到上传完成标识: {indicator}")
                    break
                except:
                    continue

            if not upload_completed:
                # 如果没有找到明确的完成标识，等待一段时间并检查页面变化
                utils.logger.info("[DouYinCrawler._perform_video_publish] 未找到明确的上传完成标识，等待页面稳定...")
                await asyncio.sleep(10)

            # 4. 填写视频描述
            utils.logger.info("[DouYinCrawler._perform_video_publish] 填写视频描述...")

            description_filled = False

            try:
                # 策略1: 查找标题输入框
                title_selectors = [
                    "input[placeholder*='填写作品标题']",
                    "input[placeholder*='作品标题']",
                    "input[placeholder*='标题']",
                    ".semi-input[placeholder*='标题']",
                    "input.semi-input-default"
                ]

                for selector in title_selectors:
                    try:
                        element = await self.context_page.wait_for_selector(selector, timeout=3000)
                        if element:
                            await element.click()
                            await self.context_page.keyboard.press("Control+a")
                            await element.fill(description)
                            description_filled = True
                            utils.logger.info(f"[DouYinCrawler._perform_video_publish] 成功填写标题，使用选择器: {selector}")
                            break
                    except Exception as e:
                        utils.logger.debug(f"[DouYinCrawler._perform_video_publish] 标题选择器 {selector} 失败: {e}")
                        continue

                # 策略2: 如果没有找到标题框，尝试查找描述框
                if not description_filled:
                    description_selectors = [
                        ".editor-kit-editor-container",
                        "[contenteditable='true']",
                        "textarea[placeholder*='简介']",
                        "textarea[placeholder*='描述']",
                        ".ace-line"
                    ]

                    for selector in description_selectors:
                        try:
                            element = await self.context_page.wait_for_selector(selector, timeout=3000)
                            if element:
                                await element.click()
                                await self.context_page.keyboard.press("Control+a")

                                # 对于contenteditable元素，使用type而不是fill
                                if selector == "[contenteditable='true']" or "editor" in selector:
                                    await self.context_page.keyboard.type(description)
                                else:
                                    await element.fill(description)

                                description_filled = True
                                utils.logger.info(f"[DouYinCrawler._perform_video_publish] 成功填写描述，使用选择器: {selector}")
                                break
                        except Exception as e:
                            utils.logger.debug(f"[DouYinCrawler._perform_video_publish] 描述选择器 {selector} 失败: {e}")
                            continue

                # 策略3: 使用JavaScript查找并填写
                if not description_filled:
                    try:
                        filled_by_js = await self.context_page.evaluate(f"""
                            (description) => {{
                                // 查找可能的输入元素
                                const selectors = [
                                    'input[placeholder*="标题"]',
                                    'input[placeholder*="作品"]',
                                    'textarea',
                                    '[contenteditable="true"]',
                                    '.editor-kit-editor-container'
                                ];

                                for (const selector of selectors) {{
                                    const elements = document.querySelectorAll(selector);
                                    for (const element of elements) {{
                                        if (element.offsetParent !== null) {{ // 元素可见
                                            element.focus();
                                            element.click();

                                            if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {{
                                                element.value = description;
                                                element.dispatchEvent(new Event('input', {{ bubbles: true }}));
                                            }} else {{
                                                element.textContent = description;
                                                element.dispatchEvent(new Event('input', {{ bubbles: true }}));
                                            }}

                                            return true;
                                        }}
                                    }}
                                }}
                                return false;
                            }}
                        """, description)

                        if filled_by_js:
                            description_filled = True
                            utils.logger.info("[DouYinCrawler._perform_video_publish] 通过JavaScript成功填写描述")
                    except Exception as e:
                        utils.logger.debug(f"[DouYinCrawler._perform_video_publish] JavaScript填写失败: {e}")

            except Exception as e:
                utils.logger.warning(f"[DouYinCrawler._perform_video_publish] 填写描述时发生错误: {e}")

            if not description_filled:
                utils.logger.warning("[DouYinCrawler._perform_video_publish] 未能填写视频描述，继续发布流程...")
            else:
                # 等待一下让内容生效
                await asyncio.sleep(1)

            # 5. 点击发布按钮
            utils.logger.info("[DouYinCrawler._perform_video_publish] 点击发布按钮...")

            publish_clicked = False

            try:
                # 策略1: 使用文本内容查找发布按钮
                try:
                    # 等待发布按钮出现
                    publish_button = await self.context_page.wait_for_selector("text=发布", timeout=5000)
                    if publish_button:
                        await publish_button.click()
                        publish_clicked = True
                        utils.logger.info("[DouYinCrawler._perform_video_publish] 成功点击发布按钮 (text=发布)")
                except Exception as e:
                    utils.logger.debug(f"[DouYinCrawler._perform_video_publish] text=发布 失败: {e}")

                # 策略2: 使用CSS选择器查找
                if not publish_clicked:
                    publish_selectors = [
                        "button:has-text('发布')",
                        "button[class*='primary']",
                        "button[class*='publish']",
                        ".publish-btn",
                        "button[type='submit']"
                    ]

                    for selector in publish_selectors:
                        try:
                            button = await self.context_page.wait_for_selector(selector, timeout=3000)
                            if button:
                                # 检查按钮是否可见和可点击
                                is_visible = await button.is_visible()
                                is_enabled = await button.is_enabled()

                                if is_visible and is_enabled:
                                    await button.click()
                                    publish_clicked = True
                                    utils.logger.info(f"[DouYinCrawler._perform_video_publish] 成功点击发布按钮: {selector}")
                                    break
                        except Exception as e:
                            utils.logger.debug(f"[DouYinCrawler._perform_video_publish] 选择器 {selector} 失败: {e}")
                            continue

                # 策略3: 使用JavaScript查找并点击发布按钮
                if not publish_clicked:
                    try:
                        clicked_by_js = await self.context_page.evaluate("""
                            () => {
                                // 查找包含"发布"文本的按钮
                                const buttons = Array.from(document.querySelectorAll('button'));

                                for (const button of buttons) {
                                    const text = button.textContent || button.innerText || '';
                                    if (text.includes('发布') && button.offsetParent !== null) {
                                        // 检查按钮是否可点击
                                        if (!button.disabled) {
                                            button.click();
                                            return true;
                                        }
                                    }
                                }

                                // 如果没找到"发布"按钮，查找可能的提交按钮
                                const submitButtons = document.querySelectorAll('button[type="submit"], .primary, .btn-primary');
                                for (const button of submitButtons) {
                                    if (button.offsetParent !== null && !button.disabled) {
                                        button.click();
                                        return true;
                                    }
                                }

                                return false;
                            }
                        """)

                        if clicked_by_js:
                            publish_clicked = True
                            utils.logger.info("[DouYinCrawler._perform_video_publish] 通过JavaScript成功点击发布按钮")
                    except Exception as e:
                        utils.logger.debug(f"[DouYinCrawler._perform_video_publish] JavaScript点击失败: {e}")

                # 策略4: 尝试键盘快捷键
                if not publish_clicked:
                    try:
                        utils.logger.info("[DouYinCrawler._perform_video_publish] 尝试使用键盘快捷键发布...")
                        await self.context_page.keyboard.press("Control+Enter")
                        publish_clicked = True
                        utils.logger.info("[DouYinCrawler._perform_video_publish] 使用键盘快捷键可能已触发发布")
                    except Exception as e:
                        utils.logger.debug(f"[DouYinCrawler._perform_video_publish] 键盘快捷键失败: {e}")

            except Exception as e:
                utils.logger.error(f"[DouYinCrawler._perform_video_publish] 点击发布按钮时发生错误: {e}")

            if not publish_clicked:
                utils.logger.error("[DouYinCrawler._perform_video_publish] 无法找到发布按钮，开始调试...")

                # 调用调试函数
                await self._debug_page_elements("publish_button_not_found")

                raise Exception("无法找到或点击发布按钮")

            # 6. 等待发布结果
            return await self._wait_for_publish_result()

        except Exception as e:
            utils.logger.error(f"[DouYinCrawler._perform_video_publish] 发布操作失败: {str(e)}")
            return {
                "status": "error",
                "message": f"发布操作失败: {str(e)}"
            }

    async def _wait_for_publish_result(self) -> Dict:
        """等待并检测发布结果

        Returns:
            Dict: 发布结果
        """
        try:
            utils.logger.info("[DouYinCrawler._wait_for_publish_result] 等待发布结果...")

            # 等待发布处理完成，最多等待60秒
            max_wait_time = 60
            check_interval = 2
            elapsed_time = 0

            while elapsed_time < max_wait_time:
                await asyncio.sleep(check_interval)
                elapsed_time += check_interval

                # 检查成功标识
                success_indicators = [
                    ":has-text('发布成功')",
                    ":has-text('发布完成')",
                    ".success-message",
                    ".publish-success",
                    "[data-testid='publish-success']"
                ]

                for indicator in success_indicators:
                    try:
                        element = await self.context_page.query_selector(indicator)
                        if element:
                            utils.logger.info(f"[DouYinCrawler._wait_for_publish_result] 检测到发布成功标识: {indicator}")
                            return {
                                "status": "success",
                                "message": "视频发布成功"
                            }
                    except:
                        continue

                # 检查失败标识
                error_indicators = [
                    ":has-text('发布失败')",
                    ":has-text('上传失败')",
                    ":has-text('审核失败')",
                    ".error-message",
                    ".publish-error",
                    "[data-testid='publish-error']"
                ]

                for indicator in error_indicators:
                    try:
                        element = await self.context_page.query_selector(indicator)
                        if element:
                            error_text = await element.text_content()
                            utils.logger.error(f"[DouYinCrawler._wait_for_publish_result] 检测到发布失败标识: {indicator}, 错误信息: {error_text}")
                            return {
                                "status": "error",
                                "message": f"视频发布失败: {error_text}"
                            }
                    except:
                        continue

                # 检查是否跳转到了其他页面（可能表示发布成功）
                current_url = self.context_page.url
                if "content/post/video" not in current_url:
                    # 如果跳转到了创作者主页或其他页面，可能表示发布成功
                    if "creator.douyin.com" in current_url:
                        utils.logger.info(f"[DouYinCrawler._wait_for_publish_result] 页面跳转到: {current_url}，可能表示发布成功")
                        return {
                            "status": "success",
                            "message": "视频发布成功（通过页面跳转判断）"
                        }

                utils.logger.info(f"[DouYinCrawler._wait_for_publish_result] 等待发布结果中... ({elapsed_time}/{max_wait_time}秒)")

            # 超时后的最终检查
            utils.logger.warning("[DouYinCrawler._wait_for_publish_result] 等待发布结果超时，进行最终状态检查...")

            # 检查页面是否还在发布页面
            current_url = self.context_page.url
            if "content/post/video" not in current_url:
                # 如果不在发布页面了，认为可能发布成功
                return {
                    "status": "success",
                    "message": "视频发布可能成功（页面已跳转，但未检测到明确的成功标识）"
                }

            # 检查是否有发布按钮仍然存在且可点击
            try:
                publish_button = await self.context_page.query_selector("button:has-text('发布')")
                if publish_button:
                    is_disabled = await publish_button.is_disabled()
                    if is_disabled:
                        return {
                            "status": "processing",
                            "message": "视频正在处理中，发布状态未确定"
                        }
                    else:
                        return {
                            "status": "error",
                            "message": "发布按钮仍然可点击，可能发布失败"
                        }
            except:
                pass

            return {
                "status": "unknown",
                "message": "无法确定发布状态，请手动检查创作者平台"
            }

        except Exception as e:
            utils.logger.error(f"[DouYinCrawler._wait_for_publish_result] 检测发布结果时发生错误: {str(e)}")
            return {
                "status": "error",
                "message": f"检测发布结果时发生错误: {str(e)}"
            }

    async def _debug_page_elements(self, context: str = ""):
        """调试页面元素，输出有用的调试信息"""
        try:
            utils.logger.info(f"[DouYinCrawler._debug_page_elements] 开始调试页面元素 - {context}")

            # 获取页面基本信息
            page_title = await self.context_page.title()
            page_url = self.context_page.url
            utils.logger.info(f"页面标题: {page_title}")
            utils.logger.info(f"页面URL: {page_url}")

            # 查找所有文件输入元素
            file_inputs = await self.context_page.evaluate("""
                () => {
                    const inputs = Array.from(document.querySelectorAll('input[type="file"]'));
                    return inputs.map(input => ({
                        id: input.id,
                        className: input.className,
                        name: input.name,
                        accept: input.accept,
                        visible: input.offsetParent !== null,
                        disabled: input.disabled
                    }));
                }
            """)

            utils.logger.info(f"找到 {len(file_inputs)} 个文件输入元素:")
            for i, input_info in enumerate(file_inputs):
                utils.logger.info(f"  输入元素 {i+1}: {input_info}")

            # 查找所有按钮元素
            buttons = await self.context_page.evaluate("""
                () => {
                    const buttons = Array.from(document.querySelectorAll('button'));
                    return buttons.map(button => ({
                        text: button.textContent || button.innerText || '',
                        className: button.className,
                        id: button.id,
                        type: button.type,
                        visible: button.offsetParent !== null,
                        disabled: button.disabled
                    })).filter(btn => btn.text.trim() !== '');
                }
            """)

            utils.logger.info(f"找到 {len(buttons)} 个按钮元素:")
            for i, button_info in enumerate(buttons):
                if i < 10:  # 只显示前10个按钮
                    utils.logger.info(f"  按钮 {i+1}: {button_info}")

            # 保存页面截图
            screenshot_path = f"debug_screenshot_{context}_{int(time.time())}.png"
            await self.context_page.screenshot(path=screenshot_path)
            utils.logger.info(f"已保存调试截图: {screenshot_path}")

        except Exception as e:
            utils.logger.error(f"[DouYinCrawler._debug_page_elements] 调试页面元素时发生错误: {e}")
